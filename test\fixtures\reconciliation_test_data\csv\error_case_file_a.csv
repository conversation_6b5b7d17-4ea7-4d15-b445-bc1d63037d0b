Date,Transaction_ID,Reference,Description,Amount,Transaction_Type,Account,Category,Currency
2024-01-15,TXN001,REF001,Valid Transaction,100.00,debit,ACC001,Test,USD
invalid-date,TXN002,REF002,Invalid Date Format,200.00,credit,ACC001,Test,USD
2024-13-45,TXN003,REF003,Invalid Date Values,300.00,debit,ACC001,Test,USD
2024-01-17,TXN004,REF004,Invalid Amount,invalid-amount,credit,ACC001,Test,USD
2024-01-18,TXN005,REF005,Missing Amount,,debit,ACC001,Test,USD
2024-01-19,TXN006,REF006,Invalid Transaction Type,400.00,invalid-type,ACC001,Test,USD
2024-01-20,TXN007,REF007,Invalid Currency,500.00,credit,ACC001,Test,INVALID
2024-01-21,TXN008,REF008,Extremely Long Transaction ID That Exceeds Normal Database Field Limits And Should Cause Validation Errors,600.00,debit,ACC001,Test,USD
2024-01-22,TXN009,REF009,Extremely Long Reference Number That Exceeds Normal Database Field Limits And Should Cause Validation Errors When Processing This Transaction Record,700.00,credit,ACC001,Test,USD
2024-01-23,TXN010,REF010,SQL Injection Test'; DROP TABLE transactions; --,800.00,debit,ACC001,Test,USD
2024-01-24,TXN011,REF011,XSS Test <script>alert('xss')</script>,900.00,credit,ACC001,Test,USD
2024-01-25,TXN012,REF012,Null Byte Test,1000.00,debit,ACC001,Test,USD
2024-01-26,TXN013,REF013,Binary Data Test ���,1100.00,credit,ACC001,Test,USD
2024-01-27,TXN014,REF014,Emoji Test 😀💰🏦,1200.00,debit,ACC001,Test,USD
2024-01-28,TXN015,REF015,Control Characters Test,1300.00,credit,ACC001,Test,USD
2024-01-29,TXN016,REF016,Very Large Amount,99999999999999.99,debit,ACC001,Test,USD
2024-01-30,TXN017,REF017,Negative Zero Amount,-0.00,credit,ACC001,Test,USD
2024-01-31,TXN018,REF018,Scientific Notation,1.23E+10,debit,ACC001,Test,USD
2024-02-01,TXN019,REF019,Hexadecimal Amount,0xFF,credit,ACC001,Test,USD
2024-02-02,TXN020,REF020,Percentage Amount,50%,debit,ACC001,Test,USD
Total_Transactions,20,,,,,,,
Total_Credits,10,,,7000.00,,,,
Total_Debits,10,,,100000000007100.00,,,,
Net_Balance,,,-100000000000100.00,,,,
Total_Amount,,,100000000014100.00,,,,
