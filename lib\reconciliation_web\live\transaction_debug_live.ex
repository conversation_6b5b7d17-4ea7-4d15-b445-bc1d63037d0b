defmodule ReconciliationWeb.TransactionDebugLive do
  use ReconciliationWeb, :live_view

  alias Reconciliation.{Transaction, ReconciliationSettings}

  @impl true
  def mount(%{"run_id" => run_id}, _session, socket) do
    # Get transactions for this run
    transactions = Reconciliation.get_transactions(run_id)

    file_a_transactions = Enum.filter(transactions, fn t ->
      t.uploaded_file.file_type == "file_a"
    end)

    file_b_transactions = Enum.filter(transactions, fn t ->
      t.uploaded_file.file_type == "file_b"
    end)

    # Get user settings
    {:ok, settings} = Reconciliation.get_or_create_settings(socket.assigns.current_user.id)

    # Debug matching for first few transactions
    debug_info = debug_matching(file_a_transactions, file_b_transactions, settings)

    {:ok,
     socket
     |> assign(:page_title, "Transaction Matching Debug")
     |> assign(:run_id, run_id)
     |> assign(:file_a_transactions, file_a_transactions)
     |> assign(:file_b_transactions, file_b_transactions)
     |> assign(:settings, settings)
     |> assign(:debug_info, debug_info)
    }
  end

  defp debug_matching(file_a_transactions, file_b_transactions, settings) do
    # Take first 10 transactions from each file for debugging
    a_sample = Enum.take(file_a_transactions, 10)
    b_sample = Enum.take(file_b_transactions, 10)

    for a_txn <- a_sample do
      matches = for b_txn <- b_sample do
        amount_match = Transaction.amount_match?(a_txn, b_txn, settings.amount_tolerance)
        date_match = Transaction.date_match?(a_txn, b_txn, settings.date_tolerance_days)
        reference_match = Transaction.reference_match?(a_txn, b_txn, nil)
        transaction_type_match = Transaction.transaction_type_match?(a_txn, b_txn)
        transaction_id_match = Transaction.transaction_id_match?(a_txn, b_txn)
        account_match = Transaction.account_match?(a_txn, b_txn)
        exact_match = amount_match and date_match and reference_match and
                      transaction_type_match and transaction_id_match and account_match

        # Calculate amount difference for debugging
        amount_diff = if a_txn.amount && b_txn.amount do
          Decimal.sub(a_txn.amount, b_txn.amount) |> Decimal.abs()
        else
          nil
        end

        # Calculate date difference for debugging
        date_diff = if a_txn.transaction_date && b_txn.transaction_date do
          Date.diff(a_txn.transaction_date, b_txn.transaction_date) |> abs()
        else
          nil
        end

        %{
          b_txn: b_txn,
          amount_match: amount_match,
          date_match: date_match,
          reference_match: reference_match,
          transaction_type_match: transaction_type_match,
          transaction_id_match: transaction_id_match,
          account_match: account_match,
          exact_match: exact_match,
          amount_difference: amount_diff,
          date_difference_days: date_diff,
          similarity_score: Transaction.similarity_score(a_txn, b_txn, [
            amount_tolerance: settings.amount_tolerance,
            date_tolerance: settings.date_tolerance_days
          ])
        }
      end

      %{
        a_txn: a_txn,
        potential_matches: matches
      }
    end
  end

  # Copy the exact match logic from MatchingEngine
  defp is_exact_match?(a_txn, b_txn, settings) do
    Transaction.amount_match?(a_txn, b_txn, settings.amount_tolerance) and
    Transaction.date_match?(a_txn, b_txn, settings.date_tolerance_days) and
    Transaction.reference_match?(a_txn, b_txn, nil)
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="max-w-7xl mx-auto p-6">
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Transaction Matching Debug</h1>
        <p class="text-gray-600">Debug information for reconciliation run <%= @run_id %></p>
      </div>

      <!-- Settings Info -->
      <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Current Settings</h2>
        <div class="grid grid-cols-3 gap-4">
          <div>
            <span class="font-medium">Amount Tolerance:</span>
            <%= Decimal.to_string(@settings.amount_tolerance) %>
          </div>
          <div>
            <span class="font-medium">Date Tolerance:</span>
            <%= @settings.date_tolerance_days %> days
          </div>
          <div>
            <span class="font-medium">Auto Exact Match:</span>
            <%= @settings.auto_match_exact %>
          </div>
        </div>
      </div>

      <!-- File Summary -->
      <div class="grid grid-cols-2 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">File A Transactions</h3>
          <p class="text-2xl font-bold text-blue-600"><%= length(@file_a_transactions) %></p>
        </div>
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">File B Transactions</h3>
          <p class="text-2xl font-bold text-green-600"><%= length(@file_b_transactions) %></p>
        </div>
      </div>

      <!-- Debug Information -->
      <div class="bg-white rounded-lg shadow-sm border p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Matching Debug (First 10 transactions)</h2>

        <%= for debug_item <- @debug_info do %>
          <div class="border-b border-gray-200 pb-6 mb-6 last:border-b-0">
            <h3 class="text-lg font-medium text-gray-900 mb-3">
              File A Transaction (Row <%= debug_item.a_txn.row_number %>)
            </h3>

            <!-- Transaction A Details -->
            <div class="bg-blue-50 p-4 rounded-lg mb-4">
              <div class="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span class="font-medium">Date:</span>
                  <%= if debug_item.a_txn.transaction_date do %>
                    <%= Date.to_string(debug_item.a_txn.transaction_date) %>
                  <% else %>
                    <span class="text-red-600">nil</span>
                  <% end %>
                </div>
                <div>
                  <span class="font-medium">Amount:</span>
                  <%= Decimal.to_string(debug_item.a_txn.amount) %>
                </div>
                <div>
                  <span class="font-medium">Reference:</span>
                  <%= debug_item.a_txn.reference || "nil" %>
                </div>
                <div>
                  <span class="font-medium">Transaction Type:</span>
                  <%= debug_item.a_txn.transaction_type || "nil" %>
                </div>
                <div>
                  <span class="font-medium">Transaction ID:</span>
                  <%= debug_item.a_txn.transaction_id || "nil" %>
                </div>
                <div>
                  <span class="font-medium">Account:</span>
                  <%= debug_item.a_txn.account || "nil" %>
                </div>
                <div class="col-span-2 md:col-span-3">
                  <span class="font-medium">Description:</span>
                  <%= debug_item.a_txn.description || "nil" %>
                </div>
              </div>
            </div>

            <!-- Potential Matches -->
            <h4 class="text-md font-medium text-gray-800 mb-2">Potential Matches from File B:</h4>
            <%= for match <- debug_item.potential_matches do %>
              <div class="bg-gray-50 p-4 rounded-lg mb-3">
                <!-- Transaction B Details -->
                <div class="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm mb-3">
                  <div>
                    <span class="font-medium">Date:</span>
                    <%= if match.b_txn.transaction_date do %>
                      <%= Date.to_string(match.b_txn.transaction_date) %>
                    <% else %>
                      <span class="text-red-600">nil</span>
                    <% end %>
                  </div>
                  <div>
                    <span class="font-medium">Amount:</span>
                    <%= Decimal.to_string(match.b_txn.amount) %>
                  </div>
                  <div>
                    <span class="font-medium">Reference:</span>
                    <%= match.b_txn.reference || "nil" %>
                  </div>
                  <div>
                    <span class="font-medium">Transaction Type:</span>
                    <%= match.b_txn.transaction_type || "nil" %>
                  </div>
                  <div>
                    <span class="font-medium">Transaction ID:</span>
                    <%= match.b_txn.transaction_id || "nil" %>
                  </div>
                  <div>
                    <span class="font-medium">Account:</span>
                    <%= match.b_txn.account || "nil" %>
                  </div>
                  <div class="col-span-2 md:col-span-3">
                    <span class="font-medium">Description:</span>
                    <%= match.b_txn.description || "nil" %>
                  </div>
                </div>

                <!-- Match Results -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm mb-2">
                  <div class={["font-medium", if(match.amount_match, do: "text-green-600", else: "text-red-600")]}>
                    Amount: <%= if match.amount_match, do: "✓", else: "✗" %>
                  </div>
                  <div class={["font-medium", if(match.date_match, do: "text-green-600", else: "text-red-600")]}>
                    Date: <%= if match.date_match, do: "✓", else: "✗" %>
                  </div>
                  <div class={["font-medium", if(match.reference_match, do: "text-green-600", else: "text-red-600")]}>
                    Reference: <%= if match.reference_match, do: "✓", else: "✗" %>
                  </div>
                  <div class={["font-medium", if(match.transaction_type_match, do: "text-green-600", else: "text-red-600")]}>
                    Type: <%= if match.transaction_type_match, do: "✓", else: "✗" %>
                  </div>
                  <div class={["font-medium", if(match.transaction_id_match, do: "text-green-600", else: "text-red-600")]}>
                    TXN ID: <%= if match.transaction_id_match, do: "✓", else: "✗" %>
                  </div>
                  <div class={["font-medium", if(match.account_match, do: "text-green-600", else: "text-red-600")]}>
                    Account: <%= if match.account_match, do: "✓", else: "✗" %>
                  </div>
                  <div class={["font-medium", if(match.exact_match, do: "text-green-600", else: "text-red-600")]}>
                    Exact: <%= if match.exact_match, do: "✓", else: "✗" %>
                  </div>
                  <div class="font-medium text-blue-600">
                    Score: <%= match.similarity_score %>%
                  </div>
                </div>

                <!-- Detailed Differences -->
                <div class="grid grid-cols-2 gap-4 text-xs text-gray-600">
                  <div>
                    <%= if match.amount_difference do %>
                      Amount Diff: <%= Decimal.to_string(match.amount_difference) %>
                    <% else %>
                      Amount Diff: N/A
                    <% end %>
                  </div>
                  <div>
                    <%= if match.date_difference_days do %>
                      Date Diff: <%= match.date_difference_days %> days
                    <% else %>
                      Date Diff: N/A
                    <% end %>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        <% end %>
      </div>
    </div>
    """
  end
end
