defmodule Reconciliation.Repo.Migrations.AddSummaryFieldsToUploadedFiles do
  use Ecto.Migration

  def change do
    alter table(:uploaded_files) do
      # File-provided summary data from CSV/Excel files
      add :file_total_transactions, :integer
      add :file_total_credits, :integer
      add :file_total_debits, :integer
      add :file_total_credit_amount, :decimal, precision: 15, scale: 2
      add :file_total_debit_amount, :decimal, precision: 15, scale: 2
      add :file_net_balance, :decimal, precision: 15, scale: 2
      add :file_total_amount, :decimal, precision: 15, scale: 2

      # Calculated summary data from processed transactions
      add :calculated_total_transactions, :integer
      add :calculated_total_credits, :integer
      add :calculated_total_debits, :integer
      add :calculated_total_credit_amount, :decimal, precision: 15, scale: 2
      add :calculated_total_debit_amount, :decimal, precision: 15, scale: 2
      add :calculated_net_balance, :decimal, precision: 15, scale: 2
      add :calculated_total_amount, :decimal, precision: 15, scale: 2

      # Validation flags
      add :summary_validation_passed, :boolean, default: false
      add :summary_validation_errors, {:array, :string}
    end
  end
end
