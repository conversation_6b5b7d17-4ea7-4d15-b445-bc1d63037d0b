defmodule Reconciliation.ReconciliationSettings do
  use Ecto.Schema
  import Ecto.Changeset

  alias Reconciliation.Accounts.User

  schema "reconciliation_settings" do
    field :amount_tolerance, :decimal, default: Decimal.new("0.01")
    field :date_tolerance_days, :integer, default: 3
    field :fuzzy_match_threshold, :decimal, default: Decimal.new("0.8")
    field :auto_match_exact, :boolean, default: true
    field :auto_match_fuzzy, :boolean, default: false
    field :default_currency, :string, default: "MWK"
    field :preferred_date_format, :string
    field :column_preferences, :map

    belongs_to :user, User

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(settings, attrs) do
    settings
    |> cast(attrs, [
      :amount_tolerance, :date_tolerance_days, :fuzzy_match_threshold,
      :auto_match_exact, :auto_match_fuzzy, :default_currency,
      :preferred_date_format, :column_preferences, :user_id
    ])
    |> validate_required([:user_id])
    |> validate_number(:amount_tolerance, greater_than_or_equal_to: 0)
    |> validate_number(:date_tolerance_days, greater_than_or_equal_to: 0)
    |> validate_number(:fuzzy_match_threshold, greater_than_or_equal_to: 0, less_than_or_equal_to: 1)
    |> validate_length(:default_currency, is: 3)
    |> validate_inclusion(:preferred_date_format, [
      "MM/DD/YYYY", "DD/MM/YYYY", "YYYY-MM-DD", "DD-MM-YYYY", "MM-DD-YYYY"
    ])
    |> foreign_key_constraint(:user_id)
    |> unique_constraint(:user_id)
  end

  @doc """
  Default settings for a new user
  """
  def default_settings do
    %{
      amount_tolerance: Decimal.new("0.01"),
      date_tolerance_days: 3,
      fuzzy_match_threshold: Decimal.new("0.8"),
      auto_match_exact: true,
      auto_match_fuzzy: false,
      default_currency: "MWK",
      preferred_date_format: "MM/DD/YYYY",
      column_preferences: %{
        "date" => ["date", "transaction_date", "date_of_transaction"],
        "amount" => ["amount", "transaction_amount", "debit", "credit"],
        "reference" => ["reference", "ref", "transaction_reference", "reference_number"],
        "description" => ["description", "details", "transaction_details", "memo"],
        "id" => ["id", "transaction_id", "unique_id"],
        "type" => ["type", "transaction_type", "debit_credit"],
        "account" => ["account", "account_number", "account_name"],
        "category" => ["category", "transaction_category"]
      }
    }
  end

  @doc """
  Gets or creates settings for a user
  """
  def get_or_create_for_user(user_id) do
    case Reconciliation.Repo.get_by(__MODULE__, user_id: user_id) do
      nil ->
        %__MODULE__{}
        |> changeset(Map.put(default_settings(), :user_id, user_id))
        |> Reconciliation.Repo.insert()
      
      settings ->
        {:ok, settings}
    end
  end

  @doc """
  Returns supported date formats
  """
  def supported_date_formats do
    [
      {"MM/DD/YYYY", "MM/DD/YYYY (US Format)"},
      {"DD/MM/YYYY", "DD/MM/YYYY (European Format)"},
      {"YYYY-MM-DD", "YYYY-MM-DD (ISO Format)"},
      {"DD-MM-YYYY", "DD-MM-YYYY"},
      {"MM-DD-YYYY", "MM-DD-YYYY"}
    ]
  end

  @doc """
  Returns supported currencies
  """
  def supported_currencies do
    [
      {"MWK", "Malawian Kwacha (MK)"},
      {"USD", "US Dollar ($)"},
      {"EUR", "Euro (€)"},
      {"GBP", "British Pound (£)"},
      {"JPY", "Japanese Yen (¥)"},
      {"CAD", "Canadian Dollar (C$)"},
      {"AUD", "Australian Dollar (A$)"},
      {"CHF", "Swiss Franc"},
      {"CNY", "Chinese Yuan"},
      {"INR", "Indian Rupee"}
    ]
  end

  @doc """
  Returns column mapping preferences for a field
  """
  def column_preferences_for(%__MODULE__{column_preferences: prefs}, field) when is_map(prefs) do
    Map.get(prefs, to_string(field), [])
  end

  def column_preferences_for(_, field) do
    default_settings().column_preferences[to_string(field)] || []
  end

  @doc """
  Updates column preferences for a specific field
  """
  def update_column_preference(%__MODULE__{column_preferences: prefs} = settings, field, columns) do
    updated_prefs = Map.put(prefs || %{}, to_string(field), columns)
    changeset(settings, %{column_preferences: updated_prefs})
  end

  @doc """
  Checks if exact matching is enabled
  """
  def auto_exact_match?(%__MODULE__{auto_match_exact: true}), do: true
  def auto_exact_match?(_), do: false

  @doc """
  Checks if fuzzy matching is enabled
  """
  def auto_fuzzy_match?(%__MODULE__{auto_match_fuzzy: true}), do: true
  def auto_fuzzy_match?(_), do: false

  @doc """
  Returns amount tolerance as float for calculations
  """
  def amount_tolerance_float(%__MODULE__{amount_tolerance: tolerance}) do
    Decimal.to_float(tolerance)
  end

  @doc """
  Returns fuzzy match threshold as float for calculations
  """
  def fuzzy_threshold_float(%__MODULE__{fuzzy_match_threshold: threshold}) do
    Decimal.to_float(threshold)
  end
end
