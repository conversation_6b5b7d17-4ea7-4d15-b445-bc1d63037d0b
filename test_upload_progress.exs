#!/usr/bin/env elixir

# Simple test script to verify upload progress tracking
# This script simulates the upload process and checks if progress events are working

Mix.install([
  {:phoenix_live_view, "~> 0.20.0"},
  {:phoenix, "~> 1.7.0"}
])

defmodule UploadProgressTest do
  @moduledoc """
  Test script to verify upload progress tracking functionality
  """

  def test_progress_tracking do
    IO.puts("Testing upload progress tracking...")

    # Simulate entry data that would come from LiveView upload
    entry = %{
      ref: "test_ref_123",
      progress: 0,
      client_size: 1024 * 1024,  # 1MB
      client_name: "test_file.csv",
      client_type: "text/csv"
    }

    # Test progress updates from 0% to 100%
    Enum.each(0..100//10, fn progress ->
      updated_entry = Map.put(entry, :progress, progress)
      IO.puts("Progress: #{progress}% - Entry: #{inspect(updated_entry)}")
      
      # Simulate the progress bar width calculation
      width = "#{progress}%"
      IO.puts("  Progress bar width: #{width}")
      
      # Simulate upload speed calculation
      if progress > 0 do
        bytes_uploaded = trunc(progress / 100 * entry.client_size)
        IO.puts("  Bytes uploaded: #{bytes_uploaded}")
      end
      
      Process.sleep(100)  # Small delay to simulate real upload
    end)

    IO.puts("✅ Progress tracking test completed!")
  end

  def test_progress_bar_colors do
    IO.puts("\nTesting progress bar colors...")
    
    file_a_color = progress_bar_color("file_a")
    file_b_color = progress_bar_color("file_b")
    default_color = progress_bar_color("unknown")
    
    IO.puts("File A color: #{file_a_color}")
    IO.puts("File B color: #{file_b_color}")
    IO.puts("Default color: #{default_color}")
    
    assert file_a_color == "bg-blue-600"
    assert file_b_color == "bg-green-600"
    assert default_color == "bg-gray-600"
    
    IO.puts("✅ Progress bar colors test passed!")
  end

  def test_upload_status_structure do
    IO.puts("\nTesting upload status structure...")
    
    # Simulate upload status data
    upload_status = %{
      start_time: System.monotonic_time(:millisecond),
      bytes_uploaded: 512 * 1024,  # 512KB
      upload_speed: 100 * 1024,    # 100KB/s
      eta_seconds: 5
    }
    
    IO.puts("Upload status: #{inspect(upload_status)}")
    
    # Test formatting functions
    speed_formatted = format_upload_speed(upload_status.upload_speed)
    eta_formatted = format_eta(upload_status.eta_seconds)
    
    IO.puts("Formatted speed: #{speed_formatted}")
    IO.puts("Formatted ETA: #{eta_formatted}")
    
    IO.puts("✅ Upload status structure test passed!")
  end

  # Helper functions (copied from the LiveView module)
  defp progress_bar_color("file_a"), do: "bg-blue-600"
  defp progress_bar_color("file_b"), do: "bg-green-600"
  defp progress_bar_color(_), do: "bg-gray-600"

  defp format_upload_speed(speed) when is_number(speed) and speed > 0 do
    cond do
      speed >= 1_000_000 -> "#{Float.round(speed / 1_000_000, 1)} MB/s"
      speed >= 1_000 -> "#{Float.round(speed / 1_000, 1)} KB/s"
      true -> "#{trunc(speed)} B/s"
    end
  end
  defp format_upload_speed(_), do: "Calculating..."

  defp format_eta(nil), do: "Calculating..."
  defp format_eta(seconds) when is_integer(seconds) and seconds > 0 do
    cond do
      seconds >= 60 -> "#{div(seconds, 60)}m #{rem(seconds, 60)}s"
      true -> "#{seconds}s"
    end
  end
  defp format_eta(_), do: "Almost done"

  defp assert(true), do: :ok
  defp assert(false), do: raise("Assertion failed")
end

# Run the tests
UploadProgressTest.test_progress_tracking()
UploadProgressTest.test_progress_bar_colors()
UploadProgressTest.test_upload_status_structure()

IO.puts("\n🎉 All tests passed! Upload progress tracking should be working correctly.")
