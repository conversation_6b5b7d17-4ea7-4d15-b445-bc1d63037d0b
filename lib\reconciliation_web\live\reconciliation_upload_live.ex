defmodule ReconciliationWeb.ReconciliationUploadLive do
  use ReconciliationWeb, :live_view
  import Phoenix.Component

  require Logger # Added this line

  alias Reconciliation.Services.{ExcelParser, MatchingEngine}

  @impl true
  def mount(_params, _session, socket) do
    user = socket.assigns.current_user

    # Create a new reconciliation run
    {:ok, run} = Reconciliation.create_reconciliation_run(%{
      name: "Reconciliation #{DateTime.utc_now() |> DateTime.to_date()}",
      user_id: user.id,
      status: "pending"
    })

    # Subscribe to upload progress updates
    Phoenix.PubSub.subscribe(Reconciliation.PubSub, "upload_progress:#{run.id}")

    {:ok,
     socket
     |> assign(:page_title, "Upload Reconciliation Files")
     |> assign(:reconciliation_run, run)
     |> assign(:file_a_uploaded, nil)
     |> assign(:file_b_uploaded, nil)
     |> assign(:file_a_client_upload_complete, false)
     |> assign(:file_b_client_upload_complete, false)
     |> assign(:processing_status, %{file_a: nil, file_b: nil})
     |> assign(:upload_status, %{})
     |> assign(:database_status, %{})
     |> assign(:form, to_form(%{"name" => run.name}))
     |> allow_upload(:file_a,
          accept: ~w(.xlsx .xls .csv),
          max_entries: 1,
          max_file_size: 50_000_000, # 50MB
          auto_upload: true,
          progress: &handle_progress/3
        )
     |> allow_upload(:file_b,
          accept: ~w(.xlsx .xls .csv),
          max_entries: 1,
          max_file_size: 50_000_000, # 50MB
          auto_upload: true,
          progress: &handle_progress/3
        )
    }
  end

  @impl true
  def handle_event("validate", _params, socket) do
    {:noreply, socket}
  end

  def handle_progress(:file_a, entry, socket) do
    require Logger
    Logger.info("FILE_A_PROGRESS: #{entry.progress}% (#{entry.ref})")
    IO.inspect(entry, label: "File A Progress Entry")
    socket = handle_progress_update(entry, socket)
    socket = handle_progress_update(entry, socket)
    # Check for client-side validation errors on the specific upload config
    upload_config_has_errors = !Enum.empty?(socket.assigns.uploads.file_a.errors)

    socket =
      if entry.progress == 100 && !upload_config_has_errors do
        assign(socket, :file_a_client_upload_complete, true)
      else
        # If progress is not 100 OR there are errors, it's not complete for button purposes
        assign(socket, :file_a_client_upload_complete, false)
      end
    {:noreply, socket}
  end

  def handle_progress(:file_b, entry, socket) do
    require Logger
    Logger.info("FILE_B_PROGRESS: #{entry.progress}% (#{entry.ref})")
    IO.inspect(entry, label: "File B Progress Entry")
    socket = handle_progress_update(entry, socket)
    socket = handle_progress_update(entry, socket)
    # Check for client-side validation errors on the specific upload config
    upload_config_has_errors = !Enum.empty?(socket.assigns.uploads.file_b.errors)

    socket =
      if entry.progress == 100 && !upload_config_has_errors do
        assign(socket, :file_b_client_upload_complete, true)
      else
        # If progress is not 100 OR there are errors, it's not complete for button purposes
        assign(socket, :file_b_client_upload_complete, false)
      end
    {:noreply, socket}
  end

  @impl true
  def handle_event("update_name", %{"name" => name}, socket) do
    {:ok, run} = Reconciliation.update_reconciliation_run(socket.assigns.reconciliation_run, %{name: name})

    {:noreply,
     socket
     |> assign(:reconciliation_run, run)
     |> assign(:form, to_form(%{"name" => name}))
    }
  end

  @impl true
  def handle_event("save", _params, socket) do
    # Process file uploads
    file_a_results = consume_uploaded_entries(socket, :file_a, &handle_file_upload(&1, &2, "file_a", socket))
    file_b_results = consume_uploaded_entries(socket, :file_b, &handle_file_upload(&1, &2, "file_b", socket))

    # Flash errors from file_a upload processing
    socket =
      case file_a_results do
        [{:error, reason}] ->
          put_flash(socket, :error, "File A processing error: #{reason}")
        _ ->
          socket
      end

    # Flash errors from file_b upload processing
    socket =
      case file_b_results do
        [{:error, reason}] ->
          put_flash(socket, :error, "File B processing error: #{reason}")
        _ ->
          socket
      end

    socket =
      socket
      |> update_file_status(:file_a, file_a_results)
      |> update_file_status(:file_b, file_b_results)

    # Check if both files are uploaded
    if socket.assigns.file_a_uploaded && socket.assigns.file_b_uploaded do
      # Start processing files
      Task.start(fn -> process_reconciliation(socket.assigns.reconciliation_run.id) end)

      socket = put_flash(socket, :info, "Files uploaded successfully! Processing reconciliation...")
      {:noreply, push_navigate(socket, to: ~p"/reconciliation/#{socket.assigns.reconciliation_run.id}/results")}
    else
      {:noreply, socket}
    end
  end

  # Handle upload cancellations
  @impl true
  def handle_event("cancel_upload", %{"ref" => ref, "type" => "file_a"}, socket) do
    socket = assign(socket, :file_a_client_upload_complete, false)
    {:noreply, cancel_upload(socket, :file_a, ref)}
  end

  def handle_event("cancel_upload", %{"ref" => ref, "type" => "file_b"}, socket) do
    socket = assign(socket, :file_b_client_upload_complete, false)
    {:noreply, cancel_upload(socket, :file_b, ref)}
  end

  def handle_event("cancel_upload", %{"ref" => ref}, socket) do
    # Fallback for old format
    socket =
      socket
      |> cancel_upload(:file_a, ref)
      |> cancel_upload(:file_b, ref)
    {:noreply, socket}
  end

  # Handle PubSub messages for database insertion progress
  @impl true
  def handle_info({:database_progress, file_id, progress}, socket) do
    database_status = Map.put(socket.assigns.database_status, file_id, progress)
    {:noreply, assign(socket, :database_status, database_status)}
  end

  def handle_info({:database_complete, file_id, result}, socket) do
    database_status = Map.put(socket.assigns.database_status, file_id, %{
      status: "complete",
      result: result,
      completed_at: DateTime.utc_now()
    })
    {:noreply, assign(socket, :database_status, database_status)}
  end

  def handle_info({:database_error, file_id, error}, socket) do
    database_status = Map.put(socket.assigns.database_status, file_id, %{
      status: "error",
      error: error,
      failed_at: DateTime.utc_now()
    })

    # Show user-friendly error message
    socket = put_flash(socket, :error, "Failed to process file: #{error}")

    {:noreply, assign(socket, :database_status, database_status)}
  end

  # Handle retry upload event
  @impl true
  def handle_event("retry_upload", %{"file_id" => file_id}, socket) do
    # Find the uploaded file and retry processing
    case Reconciliation.get_uploaded_file(file_id) do
      nil ->
        {:noreply, put_flash(socket, :error, "File not found")}

      uploaded_file ->
        # Reset database status
        database_status = Map.delete(socket.assigns.database_status, uploaded_file.id)
        socket = assign(socket, :database_status, database_status)

        # Restart processing
        Task.start(fn ->
          process_file_with_progress(uploaded_file, socket.assigns.reconciliation_run.id)
        end)

        {:noreply, put_flash(socket, :info, "Retrying file processing...")}
    end
  end

  # Handle upload progress updates
  defp handle_progress_update(entry, socket) do
    upload_status = Map.get(socket.assigns.upload_status, entry.ref, %{})

    # Calculate upload speed and ETA
    current_time = System.monotonic_time(:millisecond)
    bytes_uploaded = trunc(entry.progress / 100 * entry.client_size)

    updated_status =
      case Map.get(upload_status, :start_time) do
        nil ->
          Map.merge(upload_status, %{
            start_time: current_time,
            bytes_uploaded: bytes_uploaded,
            upload_speed: 0,
            eta_seconds: nil
          })

        start_time ->
          elapsed_ms = current_time - start_time
          if elapsed_ms > 0 do
            upload_speed = bytes_uploaded / (elapsed_ms / 1000) # bytes per second
            remaining_bytes = entry.client_size - bytes_uploaded
            eta_seconds = if upload_speed > 0, do: trunc(remaining_bytes / upload_speed), else: nil

            Map.merge(upload_status, %{
              bytes_uploaded: bytes_uploaded,
              upload_speed: upload_speed,
              eta_seconds: eta_seconds
            })
          else
            upload_status
          end
      end

    assign(socket, :upload_status, Map.put(socket.assigns.upload_status, entry.ref, updated_status))
  end

  # Handle file upload and create database record
  defp handle_file_upload(%{path: temp_path} = _meta, entry, file_type, socket) do
    try do
      # Define a permanent storage directory
      # Ensure this directory is writable by your application process
      # For a typical Phoenix app, priv/static/uploads is common for accessible files,
      # or a configured path outside the app for more persistent storage.
      # Using priv/repo/uploads as an example for non-static, persistent files.
      uploads_dir_base = Application.app_dir(:reconciliation, "priv/repo/uploads")
      run_specific_uploads_dir = Path.join(uploads_dir_base, "reconciliation_runs/#{socket.assigns.reconciliation_run.id}")
      File.mkdir_p!(run_specific_uploads_dir)

      # Generate a unique filename for storage to avoid collisions
      unique_filename = generate_filename(entry.client_name)
      destination_path = Path.join(run_specific_uploads_dir, unique_filename)

      # Validate file exists at temporary path and is readable
      case File.stat(temp_path) do
        {:ok, %{size: size}} when size > 0 ->
          # Copy the file from the temporary path to the permanent destination
          File.cp!(temp_path, destination_path)
          Logger.info("[#{socket.assigns.reconciliation_run.id}] Copied uploaded file from #{temp_path} to #{destination_path}")

          # Create uploaded file record with the new permanent path
          file_attrs = %{
            reconciliation_run_id: socket.assigns.reconciliation_run.id,
            file_type: file_type,
            filename: unique_filename, # Store the unique filename used in our permanent storage
            original_filename: entry.client_name,
            file_size: size,
            mime_type: entry.client_type,
            file_path: destination_path, # Store the path to the permanent copy
            status: "uploaded"
          }

          case Reconciliation.create_uploaded_file(file_attrs) do
            {:ok, uploaded_file} ->
              # Start database insertion process with progress tracking
              Task.start(fn ->
                process_file_with_progress(uploaded_file, socket.assigns.reconciliation_run.id)
              end)
              {:ok, uploaded_file}
            {:error, changeset} ->
              error_msg = format_changeset_errors(changeset)
              {:error, "Failed to save file information: #{error_msg}"}
          end

        {:ok, %{size: 0}} ->
          {:error, "File is empty"}

        {:error, reason} ->
          {:error, "Cannot read file: #{inspect(reason)}"}
      end
    rescue
      error ->
        {:error, "Upload failed: #{Exception.message(error)}"}
    end
  end

  # Update file status in socket assigns
  defp update_file_status(socket, file_type, results) do
    case results do
      [{:ok, uploaded_file}] ->
        assign(socket, String.to_atom("#{file_type}_uploaded"), uploaded_file)
      _ ->
        socket
    end
  end

  # Generate unique filename
  defp generate_filename(original_name) do
    timestamp = DateTime.utc_now() |> DateTime.to_unix()
    extension = Path.extname(original_name)
    base_name = Path.basename(original_name, extension)
    "#{base_name}_#{timestamp}#{extension}"
  end

  # Process file with progress tracking
  defp process_file_with_progress(uploaded_file, reconciliation_run_id) do
    try do
      # Validate file still exists
      unless File.exists?(uploaded_file.file_path) do
        raise "File no longer exists: #{uploaded_file.file_path}"
      end

      # Broadcast start of database insertion
      Phoenix.PubSub.broadcast(
        Reconciliation.PubSub,
        "upload_progress:#{reconciliation_run_id}",
        {:database_progress, uploaded_file.id, %{
          status: "inserting",
          progress: 0,
          message: "Starting database insertion..."
        }}
      )

      # Update file status to processing
      case Reconciliation.update_uploaded_file(uploaded_file, %{status: "processing"}) do
        {:ok, _} -> :ok
        {:error, changeset} ->
          error_msg = format_changeset_errors(changeset)
          raise "Failed to update file status: #{error_msg}"
      end

      # Parse file with progress tracking
      case ExcelParser.parse_file_with_progress(uploaded_file, fn progress ->
        Phoenix.PubSub.broadcast(
          Reconciliation.PubSub,
          "upload_progress:#{reconciliation_run_id}",
          {:database_progress, uploaded_file.id, progress}
        )
      end) do
        {:ok, result} ->
          # Broadcast completion
          Phoenix.PubSub.broadcast(
            Reconciliation.PubSub,
            "upload_progress:#{reconciliation_run_id}",
            {:database_complete, uploaded_file.id, result}
          )

        {:error, error} ->
          # Mark file as failed and broadcast error
          Reconciliation.mark_file_failed(uploaded_file, [error])

          Phoenix.PubSub.broadcast(
            Reconciliation.PubSub,
            "upload_progress:#{reconciliation_run_id}",
            {:database_error, uploaded_file.id, format_user_friendly_error(error)}
          )
      end

    rescue
      error ->
        # Mark file as failed
        Reconciliation.mark_file_failed(uploaded_file, [Exception.message(error)])

        Phoenix.PubSub.broadcast(
          Reconciliation.PubSub,
          "upload_progress:#{reconciliation_run_id}",
          {:database_error, uploaded_file.id, format_user_friendly_error(Exception.message(error))}
        )
    end
  end

  # Process reconciliation in background
  defp process_reconciliation(reconciliation_run_id) do
    Logger.info("[#{reconciliation_run_id}] Starting background reconciliation task.")

    try do
      # Attempt to fetch the run first
      case Reconciliation.get_reconciliation_run(reconciliation_run_id) do
        nil ->
          error_message = "ReconciliationRun record not found for ID: #{reconciliation_run_id}. Cannot process."
          Logger.error("[#{reconciliation_run_id}] #{error_message}")
          # Attempt to mark as failed by ID if the run struct couldn't be fetched
          Reconciliation.mark_reconciliation_failed_by_id(reconciliation_run_id, error_message)

        %Reconciliation.ReconciliationRun{} = run ->
          Logger.info("[#{run.id}] Successfully fetched ReconciliationRun. User: #{run.user_id}")
          uploaded_files = Reconciliation.get_uploaded_files(run.id)
          Logger.info("[#{run.id}] Found #{length(uploaded_files)} uploaded files.")

          # Inner try for the main processing logic
          try do
            # Update status to processing
            case Reconciliation.update_reconciliation_run(run, %{status: "processing"}) do
              {:ok, updated_run} ->
                Logger.info("[#{updated_run.id}] Status updated to 'processing'.")
              {:error, changeset} ->
                error_msg = "Failed to update run status to 'processing': #{inspect(changeset.errors)}"
                Logger.error("[#{run.id}] #{error_msg}")
                # Mark as failed if status update fails before main work
                Reconciliation.mark_reconciliation_failed(run, error_msg)
                # The run is marked as failed. Subsequent parsing/matching for this run will not occur
                # because we are in the :error branch of the case statement.
            end

            Logger.info("[#{run.id}] Starting parsing of uploaded files.")
            Enum.each(uploaded_files, fn file ->
              Logger.info("[#{run.id}] Parsing file: #{file.original_filename} (ID: #{file.id})")
              ExcelParser.parse_file(file)
            end)
            Logger.info("[#{run.id}] Completed parsing of uploaded files.")

            Logger.info("[#{run.id}] Starting transaction matching via MatchingEngine.")
            MatchingEngine.match_transactions(run)
            Logger.info("[#{run.id}] MatchingEngine.match_transactions completed. Stats will be calculated and status set to 'completed'.")

          rescue
            inner_error ->
              error_message = "Error during reconciliation processing: #{Exception.message(inner_error)}\nStacktrace: #{inspect(Exception.stacktrace(inner_error))}"
              Logger.error("[#{run.id}] #{error_message}")
              Reconciliation.mark_reconciliation_failed(run, error_message)
          end
      end
    rescue
      outer_error ->
        # This catches errors from Reconciliation.get_reconciliation_run! (if used) or other unexpected issues before run is fetched.
        error_message = "Outer error in process_reconciliation for ID #{reconciliation_run_id}: #{Exception.message(outer_error)}\nStacktrace: #{inspect(Exception.stacktrace(outer_error))}"
        Logger.error(error_message)
        # If run_id is known, attempt to mark as failed by ID
        if reconciliation_run_id do
          Reconciliation.mark_reconciliation_failed_by_id(reconciliation_run_id, "Outer processing error: #{Exception.message(outer_error)}")
        end
    end
  end

  defp error_to_string(:too_large), do: "File too large (max 50MB)"
  defp error_to_string(:too_many_files), do: "Too many files"
  defp error_to_string(:not_accepted), do: "File type not accepted (.xlsx, .xls, .csv only)"
  defp error_to_string(_), do: "Unknown error"

  # Error Alert Component
  defp error_alert(assigns) do
    ~H"""
    <div class="bg-red-50 border border-red-200 rounded-lg p-3">
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <.icon name="hero-exclamation-triangle" class="w-5 h-5 text-red-400" />
        </div>
        <div class="ml-3 flex-1">
          <h3 class="text-sm font-medium text-red-800">
            Upload Error
          </h3>
          <div class="mt-1 text-sm text-red-700">
            <%= error_to_string(@error) %>
          </div>
          <div class="mt-2">
            <button
              type="button"
              class="text-xs bg-red-100 hover:bg-red-200 text-red-800 px-2 py-1 rounded transition-colors"
              phx-click="dismiss_error"
            >
              Dismiss
            </button>
          </div>
        </div>
      </div>
    </div>
    """
  end

  # Format user-friendly error messages
  defp format_user_friendly_error(error) when is_binary(error) do
    cond do
      String.contains?(error, "Unsupported file format") ->
        "This file format is not supported. Please upload an Excel (.xlsx, .xls) or CSV file."

      String.contains?(error, "Failed to parse") ->
        "Unable to read the file. Please check that the file is not corrupted and try again."

      String.contains?(error, "File is empty") ->
        "The uploaded file appears to be empty. Please check your file and try again."

      String.contains?(error, "Missing or invalid amount") ->
        "Some rows are missing required amount values. Please check your data and try again."

      String.contains?(error, "no data") ->
        "No transaction data found in the file. Please check that your file contains the expected data."

      true ->
        "An error occurred while processing your file. Please try again or contact support if the problem persists."
    end
  end
  defp format_user_friendly_error(error), do: "An unexpected error occurred: #{inspect(error)}"

  # Format changeset errors for display
  defp format_changeset_errors(changeset) do
    changeset.errors
    |> Enum.map(fn {field, {message, _}} -> "#{field}: #{message}" end)
    |> Enum.join(", ")
  end

  # Upload Progress Card Component
  defp upload_progress_card(assigns) do
    ~H"""
    <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
      <!-- File Info Header -->
      <div class="flex items-center justify-between mb-3">
        <div class="flex items-center space-x-3">
          <div class="flex-shrink-0">
            <.icon name="hero-document" class="w-5 h-5 text-gray-400" />
          </div>
          <div class="min-w-0 flex-1">
            <p class="text-sm font-medium text-gray-900 truncate">
              <%= @entry.client_name %>
            </p>
            <p class="text-xs text-gray-500">
              <%= format_file_size(@entry.client_size) %>
            </p>
          </div>
        </div>
        <button
          type="button"
          phx-click="cancel_upload"
          phx-value-ref={@entry.ref}
          phx-value-type={@file_type}
          class="text-gray-400 hover:text-red-500 transition-colors"
        >
          <.icon name="hero-x-mark" class="w-4 h-4" />
        </button>
      </div>

      <!-- Upload Progress Section -->
      <div class="mb-4">
        <div class="flex items-center justify-between text-xs text-gray-600 mb-2">
          <span class="font-medium">File Upload</span>
          <span class="font-mono"><%= @entry.progress %>%</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-3">
          <div
            class={"h-3 rounded-full transition-all duration-300 #{progress_bar_color(@file_type)}"}
            style={"width: #{@entry.progress}%"}
          ></div>
        </div>

        <!-- Upload Stats -->
        <%= if @entry.progress > 0 and @entry.progress < 100 do %>
          <div class="grid grid-cols-2 gap-4 text-xs text-gray-500 mt-2">
            <div>
              <span class="font-medium">Speed:</span>
              <%= format_upload_speed(Map.get(@upload_status, :upload_speed, 0)) %>
            </div>
            <div>
              <span class="font-medium">ETA:</span>
              <%= format_eta(Map.get(@upload_status, :eta_seconds)) %>
            </div>
          </div>
        <% end %>

        <!-- Upload Status Messages -->
        <%= if @entry.progress == 100 do %>
          <div class="flex items-center text-xs text-green-600 mt-2">
            <.icon name="hero-check-circle" class="w-4 h-4 mr-1" />
            <span>Upload complete - Processing file...</span>
          </div>
        <% end %>
      </div>

      <!-- Database Processing Section -->
      <%= if @database_status do %>
        <div class="border-t border-gray-200 pt-4">
          <div class="flex items-center justify-between text-xs text-gray-600 mb-2">
            <span class="font-medium">Database Processing</span>
            <%= if Map.get(@database_status, :progress) do %>
              <span class="font-mono"><%= @database_status.progress %>%</span>
            <% end %>
          </div>

          <.database_status_indicator status={@database_status} file_type={@file_type} />
        </div>
      <% end %>
    </div>
    """
  end

  # Database Status Indicator Component
  defp database_status_indicator(assigns) do
    ~H"""
    <div class="space-y-3">
      <%= case @status.status do %>
        <% "inserting" -> %>
          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <div class="flex items-center text-sm text-blue-600">
                <.icon name="hero-arrow-path" class="w-4 h-4 mr-2 animate-spin" />
                <span><%= @status.message || "Inserting records into database..." %></span>
              </div>
              <%= if Map.get(@status, :records_processed) && Map.get(@status, :total_records) do %>
                <span class="text-xs text-gray-500 font-mono">
                  <%= @status.records_processed %>/<%= @status.total_records %>
                </span>
              <% end %>
            </div>
            <%= if Map.has_key?(@status, :progress) and @status.progress > 0 do %>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class={"bg-blue-600 h-2 rounded-full transition-all duration-300 #{progress_bar_color(@file_type)}"} style={"width: #{@status.progress}%"}></div>
              </div>
              <div class="flex justify-between text-xs text-gray-500">
                <span>Processing records...</span>
                <span><%= @status.progress %>%</span>
              </div>
            <% end %>
          </div>

        <% "parsing" -> %>
          <div class="space-y-2">
            <div class="flex items-center text-sm text-blue-600">
              <.icon name="hero-arrow-path" class="w-4 h-4 mr-2 animate-spin" />
              <span><%= @status.message || "Parsing file structure..." %></span>
            </div>
            <%= if Map.has_key?(@status, :progress) and @status.progress > 0 do %>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class={"bg-blue-600 h-2 rounded-full transition-all duration-300 #{progress_bar_color(@file_type)}"} style={"width: #{@status.progress}%"}></div>
              </div>
              <div class="flex justify-between text-xs text-gray-500">
                <span>Reading file content...</span>
                <span><%= @status.progress %>%</span>
              </div>
            <% end %>
          </div>

        <% "processing" -> %>
          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <div class="flex items-center text-sm text-blue-600">
                <.icon name="hero-arrow-path" class="w-4 h-4 mr-2 animate-spin" />
                <span><%= @status.message || "Processing transaction data..." %></span>
              </div>
              <%= if Map.get(@status, :records_processed) && Map.get(@status, :total_records) do %>
                <span class="text-xs text-gray-500 font-mono">
                  <%= @status.records_processed %>/<%= @status.total_records %>
                </span>
              <% end %>
            </div>
            <%= if Map.has_key?(@status, :progress) and @status.progress > 0 do %>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class={"bg-blue-600 h-2 rounded-full transition-all duration-300 #{progress_bar_color(@file_type)}"} style={"width: #{@status.progress}%"}></div>
              </div>
              <div class="flex justify-between text-xs text-gray-500">
                <span>Validating and storing records...</span>
                <span><%= @status.progress %>%</span>
              </div>
            <% end %>
          </div>

        <% "complete" -> %>
          <div class="space-y-2">
            <div class="flex items-center text-sm text-green-600">
              <.icon name="hero-check-circle" class="w-4 h-4 mr-2" />
              <span>Database processing complete!</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class={"bg-green-600 h-2 rounded-full #{progress_bar_color(@file_type)}"} style="width: 100%"></div>
            </div>
            <%= if Map.has_key?(@status, :result) do %>
              <div class="grid grid-cols-2 gap-4 text-xs text-gray-600">
                <%= if Map.get(@status.result, :rows_inserted) do %>
                  <div>
                    <span class="font-medium">Records:</span>
                    <%= @status.result.rows_inserted %>
                  </div>
                <% end %>
                <%= if Map.get(@status.result, :processing_time) do %>
                  <div>
                    <span class="font-medium">Time:</span>
                    <%= format_processing_time(@status.result.processing_time) %>
                  </div>
                <% end %>
              </div>
            <% end %>
          </div>

        <% "error" -> %>
          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <div class="flex items-center text-sm text-red-600">
                <.icon name="hero-exclamation-triangle" class="w-4 h-4 mr-2" />
                <span>Processing failed</span>
              </div>
              <button
                type="button"
                phx-click="retry_upload"
                phx-value-file_id={get_file_id_from_status(@status)}
                class="text-xs bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded transition-colors font-medium"
              >
                Retry
              </button>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-red-600 h-2 rounded-full" style="width: 100%"></div>
            </div>
            <%= if Map.has_key?(@status, :error) do %>
              <div class="bg-red-50 border border-red-200 rounded p-2">
                <div class="text-xs text-red-700 font-medium mb-1">Error Details:</div>
                <div class="text-xs text-red-600">
                  <%= @status.error %>
                </div>
              </div>
            <% end %>
          </div>

        <% _ -> %>
          <div class="space-y-2">
            <div class="flex items-center text-sm text-gray-500">
              <.icon name="hero-clock" class="w-4 h-4 mr-2" />
              <span>Waiting to start database processing...</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-gray-400 h-2 rounded-full" style="width: 0%"></div>
            </div>
          </div>
      <% end %>
    </div>
    """
  end

  # Helper functions for formatting
  defp format_file_size(bytes) when is_integer(bytes) do
    cond do
      bytes >= 1_000_000 -> "#{Float.round(bytes / 1_000_000, 1)} MB"
      bytes >= 1_000 -> "#{Float.round(bytes / 1_000, 1)} KB"
      true -> "#{bytes} bytes"
    end
  end
  defp format_file_size(_), do: "Unknown size"

  defp format_upload_speed(speed) when is_number(speed) and speed > 0 do
    cond do
      speed >= 1_000_000 -> "#{Float.round(speed / 1_000_000, 1)} MB/s"
      speed >= 1_000 -> "#{Float.round(speed / 1_000, 1)} KB/s"
      true -> "#{trunc(speed)} B/s"
    end
  end
  defp format_upload_speed(_), do: "Calculating..."

  defp format_eta(nil), do: "Calculating..."
  defp format_eta(seconds) when is_integer(seconds) and seconds > 0 do
    cond do
      seconds >= 60 -> "#{div(seconds, 60)}m #{rem(seconds, 60)}s"
      true -> "#{seconds}s"
    end
  end
  defp format_eta(_), do: "Almost done"

  defp progress_bar_color("file_a"), do: "bg-blue-600"
  defp progress_bar_color("file_b"), do: "bg-green-600"
  defp progress_bar_color(_), do: "bg-gray-600"

  defp format_processing_time(time_ms) when is_number(time_ms) do
    cond do
      time_ms >= 60_000 -> "#{Float.round(time_ms / 60_000, 1)}m"
      time_ms >= 1_000 -> "#{Float.round(time_ms / 1_000, 1)}s"
      true -> "#{trunc(time_ms)}ms"
    end
  end
  defp format_processing_time(_), do: "Unknown"

  # Helper function to get database status for a file entry
  defp get_database_status_for_entry(_database_status, nil), do: nil
  defp get_database_status_for_entry(database_status, uploaded_file) do
    Map.get(database_status, uploaded_file.id)
  end

  # Helper function to extract file ID from status (for retry functionality)
  defp get_file_id_from_status(status) do
    # This would need to be passed from the component context
    # For now, we'll use a placeholder
    Map.get(status, :file_id, "unknown")
  end

  # Handle dismiss error event
  @impl true
  def handle_event("dismiss_error", _params, socket) do
    {:noreply, clear_flash(socket, :error)}
  end
end
