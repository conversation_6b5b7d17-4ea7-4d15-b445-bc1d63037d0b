#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create comprehensive Excel test files for transaction reconciliation testing.
This creates two files that demonstrate various matching scenarios.
"""

import pandas as pd
from datetime import datetime, timedelta
import random

def create_test_data():
    """Create comprehensive test data for transaction matching"""
    
    # Base transactions that should match exactly when uploaded as both File A and File B
    base_transactions = [
        {
            'Date': '2024-01-15',
            'Transaction_ID': 'TXN001',
            'Reference': 'REF123',
            'Description': 'Product Sales - Widget A',
            'Amount': 1875.25,
            'Transaction_Type': 'credit',
            'Account': 'ACC001',
            'Category': 'Sales'
        },
        {
            'Date': '2024-01-16',
            'Transaction_ID': 'TXN002',
            'Reference': 'REF124',
            'Description': 'Service Payment - Consulting',
            'Amount': 2500.00,
            'Transaction_Type': 'credit',
            'Account': 'ACC002',
            'Category': 'Services'
        },
        {
            'Date': '2024-01-17',
            'Transaction_ID': 'TXN003',
            'Reference': 'REF125',
            'Description': 'Office Supplies Purchase',
            'Amount': -450.75,
            'Transaction_Type': 'debit',
            'Account': 'ACC003',
            'Category': 'Expenses'
        },
        {
            'Date': '2024-01-18',
            'Transaction_ID': 'TXN004',
            'Reference': None,  # Test nil reference matching
            'Description': 'Cash Deposit',
            'Amount': 1000.00,
            'Transaction_Type': 'credit',
            'Account': 'ACC001',
            'Category': 'Deposits'
        },
        {
            'Date': None,  # Test nil date matching
            'Transaction_ID': 'TXN005',
            'Reference': 'REF126',
            'Description': 'Bank Fee',
            'Amount': -25.00,
            'Transaction_Type': 'debit',
            'Account': 'ACC001',
            'Category': 'Fees'
        },
        {
            'Date': '2024-01-20',
            'Transaction_ID': None,  # Test nil transaction ID matching
            'Reference': 'REF127',
            'Description': 'Interest Payment',
            'Amount': 15.50,
            'Transaction_Type': 'credit',
            'Account': 'ACC002',
            'Category': 'Interest'
        },
        {
            'Date': '2024-01-21',
            'Transaction_ID': 'TXN006',
            'Reference': 'REF128',
            'Description': 'Equipment Purchase',
            'Amount': -3500.00,
            'Transaction_Type': 'debit',
            'Account': None,  # Test nil account matching
            'Category': 'Equipment'
        }
    ]
    
    return base_transactions

def create_identical_files():
    """Create two identical files for perfect matching test"""
    
    transactions = create_test_data()
    
    # Create DataFrame
    df = pd.DataFrame(transactions)
    
    # Save as File A
    df.to_excel('test_file_a_identical.xlsx', index=False, sheet_name='Transactions')
    
    # Save as File B (identical copy)
    df.to_excel('test_file_b_identical.xlsx', index=False, sheet_name='Transactions')
    
    print("✅ Created identical test files:")
    print("   - test_file_a_identical.xlsx")
    print("   - test_file_b_identical.xlsx")
    print(f"   - Contains {len(transactions)} transactions")
    print("   - All transactions should match with 100% confidence")

def create_mixed_scenario_files():
    """Create files with various matching scenarios"""
    
    # File A transactions
    file_a_transactions = [
        # Perfect matches (should match exactly)
        {
            'Date': '2024-01-15',
            'Transaction_ID': 'TXN001',
            'Reference': 'REF123',
            'Description': 'Product Sales - Widget A',
            'Amount': 1875.25,
            'Transaction_Type': 'credit',
            'Account': 'ACC001',
            'Category': 'Sales'
        },
        {
            'Date': '2024-01-16',
            'Transaction_ID': 'TXN002',
            'Reference': 'REF124',
            'Description': 'Service Payment',
            'Amount': 2500.00,
            'Transaction_Type': 'credit',
            'Account': 'ACC002',
            'Category': 'Services'
        },
        
        # Should NOT match - different transaction type
        {
            'Date': '2024-01-17',
            'Transaction_ID': 'TXN003',
            'Reference': 'REF125',
            'Description': 'Office Supplies',
            'Amount': 450.75,
            'Transaction_Type': 'credit',  # Different from File B (debit)
            'Account': 'ACC003',
            'Category': 'Expenses'
        },
        
        # Should NOT match - different transaction ID
        {
            'Date': '2024-01-18',
            'Transaction_ID': 'TXN004',  # Different from File B (TXN004B)
            'Reference': 'REF126',
            'Description': 'Cash Deposit',
            'Amount': 1000.00,
            'Transaction_Type': 'credit',
            'Account': 'ACC001',
            'Category': 'Deposits'
        },
        
        # Should NOT match - different account
        {
            'Date': '2024-01-19',
            'Transaction_ID': 'TXN005',
            'Reference': 'REF127',
            'Description': 'Bank Fee',
            'Amount': -25.00,
            'Transaction_Type': 'debit',
            'Account': 'ACC001',  # Different from File B (ACC002)
            'Category': 'Fees'
        },
        
        # Unmatched transaction (only in File A)
        {
            'Date': '2024-01-20',
            'Transaction_ID': 'TXN006',
            'Reference': 'REF128',
            'Description': 'Equipment Purchase',
            'Amount': -3500.00,
            'Transaction_Type': 'debit',
            'Account': 'ACC003',
            'Category': 'Equipment'
        },
        
        # Fuzzy match candidate (similar but not exact)
        {
            'Date': '2024-01-21',
            'Transaction_ID': 'TXN007',
            'Reference': 'REF129',
            'Description': 'Software License',
            'Amount': 299.99,
            'Transaction_Type': 'debit',
            'Account': 'ACC002',
            'Category': 'Software'
        }
    ]
    
    # File B transactions
    file_b_transactions = [
        # Perfect matches (should match exactly with File A)
        {
            'Date': '2024-01-15',
            'Transaction_ID': 'TXN001',
            'Reference': 'REF123',
            'Description': 'Product Sales - Widget A',
            'Amount': 1875.25,
            'Transaction_Type': 'credit',
            'Account': 'ACC001',
            'Category': 'Sales'
        },
        {
            'Date': '2024-01-16',
            'Transaction_ID': 'TXN002',
            'Reference': 'REF124',
            'Description': 'Service Payment',
            'Amount': 2500.00,
            'Transaction_Type': 'credit',
            'Account': 'ACC002',
            'Category': 'Services'
        },
        
        # Should NOT match - different transaction type
        {
            'Date': '2024-01-17',
            'Transaction_ID': 'TXN003',
            'Reference': 'REF125',
            'Description': 'Office Supplies',
            'Amount': 450.75,
            'Transaction_Type': 'debit',  # Different from File A (credit)
            'Account': 'ACC003',
            'Category': 'Expenses'
        },
        
        # Should NOT match - different transaction ID
        {
            'Date': '2024-01-18',
            'Transaction_ID': 'TXN004B',  # Different from File A (TXN004)
            'Reference': 'REF126',
            'Description': 'Cash Deposit',
            'Amount': 1000.00,
            'Transaction_Type': 'credit',
            'Account': 'ACC001',
            'Category': 'Deposits'
        },
        
        # Should NOT match - different account
        {
            'Date': '2024-01-19',
            'Transaction_ID': 'TXN005',
            'Reference': 'REF127',
            'Description': 'Bank Fee',
            'Amount': -25.00,
            'Transaction_Type': 'debit',
            'Account': 'ACC002',  # Different from File A (ACC001)
            'Category': 'Fees'
        },
        
        # Unmatched transaction (only in File B)
        {
            'Date': '2024-01-22',
            'Transaction_ID': 'TXN008',
            'Reference': 'REF130',
            'Description': 'Marketing Expense',
            'Amount': -750.00,
            'Transaction_Type': 'debit',
            'Account': 'ACC001',
            'Category': 'Marketing'
        },
        
        # Fuzzy match candidate (similar to File A TXN007 but different)
        {
            'Date': '2024-01-21',
            'Transaction_ID': 'TXN007B',  # Different ID
            'Reference': 'REF129B',      # Different reference
            'Description': 'Software License Annual',  # Similar description
            'Amount': 300.00,            # Similar amount (within tolerance)
            'Transaction_Type': 'debit',
            'Account': 'ACC002',
            'Category': 'Software'
        }
    ]
    
    # Create DataFrames
    df_a = pd.DataFrame(file_a_transactions)
    df_b = pd.DataFrame(file_b_transactions)
    
    # Save files
    df_a.to_excel('test_file_a_mixed.xlsx', index=False, sheet_name='Transactions')
    df_b.to_excel('test_file_b_mixed.xlsx', index=False, sheet_name='Transactions')
    
    print("\n✅ Created mixed scenario test files:")
    print("   - test_file_a_mixed.xlsx")
    print("   - test_file_b_mixed.xlsx")
    print(f"   - File A: {len(file_a_transactions)} transactions")
    print(f"   - File B: {len(file_b_transactions)} transactions")
    print("   - Expected results:")
    print("     * 2 exact matches (TXN001, TXN002)")
    print("     * 3 non-matches due to different fields (TXN003, TXN004, TXN005)")
    print("     * 2 unmatched transactions (TXN006 in A, TXN008 in B)")
    print("     * 1 potential fuzzy match (TXN007 vs TXN007B)")

def create_edge_case_files():
    """Create files to test edge cases and nil value handling"""
    
    # File A - Edge cases
    file_a_edge = [
        # All nil optional fields (should match with File B)
        {
            'Date': None,
            'Transaction_ID': None,
            'Reference': None,
            'Description': 'Transaction with all nil fields',
            'Amount': 100.00,
            'Transaction_Type': None,
            'Account': None,
            'Category': 'Test'
        },
        
        # Mixed nil fields (should NOT match - different nil patterns)
        {
            'Date': '2024-01-15',
            'Transaction_ID': 'TXN001',
            'Reference': None,
            'Description': 'Mixed nil test 1',
            'Amount': 200.00,
            'Transaction_Type': 'credit',
            'Account': None,
            'Category': 'Test'
        },
        
        # Amount tolerance test (should match within tolerance)
        {
            'Date': '2024-01-16',
            'Transaction_ID': 'TXN002',
            'Reference': 'REF001',
            'Description': 'Amount tolerance test',
            'Amount': 100.00,  # File B has 100.01 (within 0.01 tolerance)
            'Transaction_Type': 'credit',
            'Account': 'ACC001',
            'Category': 'Test'
        },
        
        # Date tolerance test (should match within tolerance)
        {
            'Date': '2024-01-17',  # File B has 2024-01-19 (2 days diff, within 3-day tolerance)
            'Transaction_ID': 'TXN003',
            'Reference': 'REF002',
            'Description': 'Date tolerance test',
            'Amount': 300.00,
            'Transaction_Type': 'debit',
            'Account': 'ACC002',
            'Category': 'Test'
        }
    ]
    
    # File B - Edge cases
    file_b_edge = [
        # All nil optional fields (should match with File A)
        {
            'Date': None,
            'Transaction_ID': None,
            'Reference': None,
            'Description': 'Transaction with all nil fields',
            'Amount': 100.00,
            'Transaction_Type': None,
            'Account': None,
            'Category': 'Test'
        },
        
        # Mixed nil fields (should NOT match - different nil patterns)
        {
            'Date': '2024-01-15',
            'Transaction_ID': None,  # Different from File A (has TXN001)
            'Reference': 'REF999',  # Different from File A (nil)
            'Description': 'Mixed nil test 1',
            'Amount': 200.00,
            'Transaction_Type': 'credit',
            'Account': 'ACC999',  # Different from File A (nil)
            'Category': 'Test'
        },
        
        # Amount tolerance test (should match within tolerance)
        {
            'Date': '2024-01-16',
            'Transaction_ID': 'TXN002',
            'Reference': 'REF001',
            'Description': 'Amount tolerance test',
            'Amount': 100.01,  # File A has 100.00 (within 0.01 tolerance)
            'Transaction_Type': 'credit',
            'Account': 'ACC001',
            'Category': 'Test'
        },
        
        # Date tolerance test (should match within tolerance)
        {
            'Date': '2024-01-19',  # File A has 2024-01-17 (2 days diff, within 3-day tolerance)
            'Transaction_ID': 'TXN003',
            'Reference': 'REF002',
            'Description': 'Date tolerance test',
            'Amount': 300.00,
            'Transaction_Type': 'debit',
            'Account': 'ACC002',
            'Category': 'Test'
        }
    ]
    
    # Create DataFrames
    df_a = pd.DataFrame(file_a_edge)
    df_b = pd.DataFrame(file_b_edge)
    
    # Save files
    df_a.to_excel('test_file_a_edge_cases.xlsx', index=False, sheet_name='Transactions')
    df_b.to_excel('test_file_b_edge_cases.xlsx', index=False, sheet_name='Transactions')
    
    print("\n✅ Created edge case test files:")
    print("   - test_file_a_edge_cases.xlsx")
    print("   - test_file_b_edge_cases.xlsx")
    print(f"   - File A: {len(file_a_edge)} transactions")
    print(f"   - File B: {len(file_b_edge)} transactions")
    print("   - Expected results:")
    print("     * 1 exact match (all nil fields)")
    print("     * 1 non-match (mixed nil patterns)")
    print("     * 2 tolerance matches (amount and date within tolerance)")

def main():
    """Create all test files"""
    print("Creating comprehensive Excel test files for transaction reconciliation...\n")
    
    try:
        # Create identical files for perfect matching test
        create_identical_files()
        
        # Create mixed scenario files
        create_mixed_scenario_files()
        
        # Create edge case files
        create_edge_case_files()
        
        print("\n" + "="*60)
        print("📁 ALL TEST FILES CREATED SUCCESSFULLY!")
        print("="*60)
        print("\n🧪 TEST SCENARIOS:")
        print("\n1. IDENTICAL FILES TEST:")
        print("   Upload: test_file_a_identical.xlsx and test_file_b_identical.xlsx")
        print("   Expected: 7/7 transactions should match exactly (100% match rate)")
        
        print("\n2. MIXED SCENARIOS TEST:")
        print("   Upload: test_file_a_mixed.xlsx and test_file_b_mixed.xlsx")
        print("   Expected: 2/7 exact matches, 3 non-matches, 2 unmatched")
        
        print("\n3. EDGE CASES TEST:")
        print("   Upload: test_file_a_edge_cases.xlsx and test_file_b_edge_cases.xlsx")
        print("   Expected: 3/4 matches (1 exact, 2 tolerance), 1 non-match")
        
        print("\n💡 TESTING TIPS:")
        print("   - Use the Debug Matching page to see detailed match analysis")
        print("   - Use the Data Inspection page to verify field values")
        print("   - Check logs for detailed matching information")
        print("   - Verify that enhanced matching criteria are working correctly")
        
    except Exception as e:
        print(f"❌ Error creating test files: {e}")
        print("Make sure you have pandas installed: pip install pandas openpyxl")

if __name__ == "__main__":
    main()
