<div class="max-w-full py-6">
  <!-- Header -->
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900 mb-2">Transactions</h1>
    <p class="text-gray-600">View and manage all transactions from your reconciliation runs</p>
  </div>

  <!-- Filters and Search -->
  <div class="bg-white rounded-lg shadow-sm border p-6 mb-8">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <!-- Reconciliation Run Filter -->
      <div>
        <label for="run_filter" class="block text-sm font-medium text-gray-700 mb-2">
          Filter by Reconciliation Run
        </label>
        <select 
          id="run_filter"
          phx-change="filter_by_run"
          name="run_id"
          class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
        >
          <option value="">All Reconciliation Runs</option>
          <%= for run <- @reconciliation_runs do %>
            <option value={run.id} selected={@selected_run_id == to_string(run.id)}>
              <%= run.name %> - <%= Calendar.strftime(run.inserted_at, "%b %d, %Y") %>
            </option>
          <% end %>
        </select>
      </div>

      <!-- Search -->
      <div>
        <label for="search" class="block text-sm font-medium text-gray-700 mb-2">
          Search Transactions
        </label>
        <.form for={%{}} phx-change="search" class="relative">
          <input
            type="text"
            name="search"
            value={@search_query}
            placeholder="Search by description, reference, or amount..."
            class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 pl-10"
          />
          <.icon name="hero-magnifying-glass" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
        </.form>
      </div>
    </div>
  </div>

  <!-- Totals Summary -->
  <div class="bg-white rounded-lg shadow-sm border p-6 mb-8">
    <h2 class="text-xl font-semibold text-gray-800 mb-4">Summary</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <p class="text-sm font-medium text-gray-500">Total Debits</p>
        <p class="text-2xl font-bold text-red-600">
          <%= if assigns[:total_debits] do %>
            <%= format_currency(@total_debits) %>
          <% else %>
            <%= format_currency(Decimal.new(0)) %>
          <% end %>
        </p>
      </div>
      <div>
        <p class="text-sm font-medium text-gray-500">Total Credits</p>
        <p class="text-2xl font-bold text-green-600">
          <%= if assigns[:total_credits] do %>
            <%= format_currency(@total_credits) %>
          <% else %>
            <%= format_currency(Decimal.new(0)) %>
          <% end %>
        </p>
      </div>
    </div>
  </div>

  <!-- Transactions Table -->
  <div class="bg-white rounded-lg shadow-sm border">
    <%= if Enum.any?(@transactions) do %>
      <div>
        <table class="table-fixed divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="w-20 px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                ID
              </th>
              <th class="w-28 px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                Date
              </th>
              <th class="w-2/5 px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                Description
              </th>
              <th class="w-32 px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                Reference
              </th>
              <th class="w-24 px-2 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                Debit
              </th>
              <th class="w-24 px-2 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                Credit
              </th>
              <th class="w-24 px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                Source
              </th>
              <th class="w-24 px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                Status
              </th>
              <th class="w-24 px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <%= for transaction <- @transactions do %>
              <tr class="hover:bg-gray-50">
                <td class="px-2 py-4 text-sm text-gray-900">
                  <div class="break-words break-all">
                    <%= transaction.id %>
                  </div>
                </td>
                <td class="px-2 py-4 text-sm text-gray-900">
                  <%= format_date(transaction.transaction_date) %>
                </td>
                <td class="px-2 py-4 text-sm text-gray-900">
                  <div class="break-words break-all">
                    <%= transaction.description || "-" %>
                  </div>
                </td>
                <td class="px-2 py-4 text-sm text-gray-900">
                  <div class="break-words break-all">
                    <%= transaction.reference || "-" %>
                  </div>
                </td>
                <td class="px-2 py-4 text-sm font-medium text-right">
                  <%= if Decimal.negative?(transaction.amount) do %>
                    <span class="text-red-600">
                      <%= format_currency(Decimal.abs(transaction.amount)) %>
                    </span>
                  <% else %>
                    -
                  <% end %>
                </td>
                <td class="px-2 py-4 text-sm font-medium text-right">
                  <%= if Decimal.positive?(transaction.amount) do %>
                    <span class="text-green-600">
                      <%= format_currency(transaction.amount) %>
                    </span>
                  <% else %>
                    -
                  <% end %>
                </td>
                <td class="px-2 py-4">
                  <%= file_type_badge(transaction.uploaded_file.file_type) %>
                </td>
                <td class="px-2 py-4">
                  <%= match_status_badge(transaction.is_matched) %>
                </td>
                <td class="px-2 py-4 text-sm font-medium">
                  <.link 
                    navigate={~p"/reconciliation/#{transaction.reconciliation_run_id}/results"}
                    class="text-indigo-600 hover:text-indigo-900"
                  >
                    View Run
                  </.link>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>

      <!-- Pagination placeholder -->
      <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
        <div class="flex items-center justify-between">
          <div class="flex-1 flex justify-between sm:hidden">
            <!-- Mobile pagination controls -->
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                Showing <span class="font-medium"><%= length(@transactions) %></span> transactions
              </p>
            </div>
          </div>
        </div>
      </div>
    <% else %>
      <div class="text-center py-12">
        <.icon name="hero-document-text" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">No transactions found</h3>
        <p class="text-gray-500 mb-6">
          <%= if @selected_run_id || @search_query != "" do %>
            Try adjusting your filters or search criteria.
          <% else %>
            Start by creating a reconciliation run to see transactions here.
          <% end %>
        </p>
        <%= unless @selected_run_id || @search_query != "" do %>
          <.link navigate={~p"/reconciliation"} class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium">
            Create Reconciliation Run
          </.link>
        <% end %>
      </div>
    <% end %>
  </div>
</div>
