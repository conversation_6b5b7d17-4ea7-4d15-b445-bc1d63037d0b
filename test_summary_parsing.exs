# Simple test script to verify summary row parsing logic
# Run with: elixir test_summary_parsing.exs

defmodule SummaryParsingTest do
  # Simulate the summary row detection logic
  def separate_transaction_and_summary_rows(data_rows) do
    Enum.split_with(data_rows, fn row ->
      # Check if this is a summary row by looking at the first column
      case Enum.at(row, 0) do
        nil -> true  # Empty row, treat as transaction
        "" -> true   # Empty first column, treat as transaction
        first_col when is_binary(first_col) ->
          first_col_lower = String.downcase(String.trim(first_col))
          # Check if this is a summary row
          summary_keywords = ["total_", "net_balance"]
          is_summary = Enum.any?(summary_keywords, &String.starts_with?(first_col_lower, &1))
          not is_summary  # Return true for transaction rows, false for summary rows
        _ -> true  # Non-string first column, treat as transaction
      end
    end)
  end

  def extract_summary_data(summary_rows) do
    summary_data = %{
      file_total_transactions: nil,
      file_total_credits: nil,
      file_total_debits: nil,
      file_total_credit_amount: nil,
      file_total_debit_amount: nil,
      file_net_balance: nil,
      file_total_amount: nil
    }

    Enum.reduce(summary_rows, summary_data, fn row, acc ->
      case Enum.at(row, 0) do
        nil -> acc
        "" -> acc
        first_col when is_binary(first_col) ->
          first_col_lower = String.downcase(String.trim(first_col))

          cond do
            String.starts_with?(first_col_lower, "total_transactions") ->
              count = extract_numeric_value(row, 1)
              Map.put(acc, :file_total_transactions, count)

            String.starts_with?(first_col_lower, "total_credits") ->
              count = extract_numeric_value(row, 1)
              amount = extract_decimal_value(row, 3)
              acc
              |> Map.put(:file_total_credits, count)
              |> Map.put(:file_total_credit_amount, amount)

            String.starts_with?(first_col_lower, "total_debits") ->
              count = extract_numeric_value(row, 1)
              amount = extract_decimal_value(row, 3)
              acc
              |> Map.put(:file_total_debits, count)
              |> Map.put(:file_total_debit_amount, amount)

            String.starts_with?(first_col_lower, "net_balance") ->
              amount = extract_decimal_value(row, 3)
              Map.put(acc, :file_net_balance, amount)

            String.starts_with?(first_col_lower, "total_amount") ->
              amount = extract_decimal_value(row, 3)
              Map.put(acc, :file_total_amount, amount)

            true -> acc
          end
        _ -> acc
      end
    end)
  end

  def extract_numeric_value(row, index) do
    case Enum.at(row, index) do
      nil -> nil
      "" -> nil
      value when is_binary(value) ->
        case Integer.parse(String.trim(value)) do
          {int, ""} -> int
          _ -> nil
        end
      value when is_integer(value) -> value
      _ -> nil
    end
  end

  def extract_decimal_value(row, index) do
    case Enum.at(row, index) do
      nil -> nil
      "" -> nil
      value when is_binary(value) ->
        # Simple decimal parsing for test
        case Float.parse(String.trim(value)) do
          {float, ""} -> float
          _ -> nil
        end
      value when is_number(value) -> value
      _ -> nil
    end
  end

  def test_perfect_match_file() do
    # Simulate CSV rows from our perfect match test file
    test_rows = [
      ["2024-01-15", "TXN001", "REF12345", "Payment to Supplier ABC", "1500.00", "debit", "ACC001", "Supplies", "USD"],
      ["2024-01-16", "TXN002", "REF12346", "Customer Payment - Invoice 1001", "2500.50", "credit", "ACC002", "Revenue", "USD"],
      ["2024-01-17", "TXN003", "REF12347", "Bank Transfer to Savings", "1000.00", "transfer", "ACC001", "Transfer", "USD"],
      ["Total_Transactions", "20", "", "", "", "", "", "", ""],
      ["Total_Credits", "6", "", "12192.50", "", "", "", "", ""],
      ["Total_Debits", "14", "", "27538.30", "", "", "", "", ""],
      ["Net_Balance", "", "", "-15345.80", "", "", "", "", ""],
      ["Total_Amount", "", "", "39730.80", "", "", "", "", ""]
    ]

    {transaction_rows, summary_rows} = separate_transaction_and_summary_rows(test_rows)

    IO.puts("=== Test Results ===")
    IO.puts("Transaction rows: #{length(transaction_rows)}")
    IO.puts("Summary rows: #{length(summary_rows)}")

    IO.puts("\nTransaction rows:")
    Enum.each(transaction_rows, fn row ->
      IO.puts("  #{Enum.at(row, 0)} - #{Enum.at(row, 3)} - #{Enum.at(row, 4)}")
    end)

    IO.puts("\nSummary rows:")
    Enum.each(summary_rows, fn row ->
      IO.puts("  #{Enum.at(row, 0)} - #{Enum.at(row, 1)} - #{Enum.at(row, 3)}")
    end)

    summary_data = extract_summary_data(summary_rows)
    IO.puts("\nExtracted summary data:")
    IO.inspect(summary_data, pretty: true)

    # Verify expected values
    assert summary_data.file_total_transactions == 20
    assert summary_data.file_total_credits == 6
    assert summary_data.file_total_debits == 14
    assert summary_data.file_total_credit_amount == 12192.50
    assert summary_data.file_total_debit_amount == 27538.30
    assert summary_data.file_net_balance == -15345.80
    assert summary_data.file_total_amount == 39730.80

    IO.puts("\n✅ All assertions passed!")
  end

  defp assert(true), do: :ok
  defp assert(false), do: raise("Assertion failed")
end

# Run the test
SummaryParsingTest.test_perfect_match_file()
